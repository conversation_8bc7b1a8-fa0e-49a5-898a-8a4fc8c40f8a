import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/components/component.dart';
import 'package:vibeo/constants/video_tiles.dart';
import 'package:vibeo/cubits/promotion/promotion_cubit.dart';
import 'package:vibeo/cubits/promotion/promotion_state.dart';

import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';

import 'package:vibeo/logic/venue/bloc/venue_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/routes/route.dart';

import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

import 'package:vibeo/utils/utils.dart';

import 'package:vibeo/widgets/home/<USER>';
import 'package:vibeo/widgets/venue/special_venue_tile.dart';

class SponsorSectionTile extends StatefulWidget {
  const SponsorSectionTile({super.key});

  @override
  State<SponsorSectionTile> createState() => _SponsorSectionTileState();
}

class _SponsorSectionTileState extends State<SponsorSectionTile>
    with TickerProviderStateMixin {
  // ScrollController for venue list
  final ScrollController _venueScrollController = ScrollController();
  final List<OfferModel> specialOffers = [];
  // Cache to store offers by venue ID
  final Map<String, List<OfferModel>> _offersCache = {};
  // Track venues that are currently being fetched to prevent duplicate fetches
  final Set<String> _currentlyFetchingVenues = {};
  bool _isFetchingOffers = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _fadeController,
        curve: Curves.easeInOut,
      ),
    );

    // Start with fully visible offers
    _fadeController.value = 1.0;

    // Add page change listener
    _pageController.addListener(_onPageChanged);
  }

  void _onPageChanged() {
    if (!_pageController.hasClients) return;

    // Calculate current page
    final page = _pageController.page!.round();

    // Update selected venue if needed
    if (page != _selectedVenueIndex) {
      // Start fade out animation
      _fadeController.reverse().then((_) {
        // Update index after fade out
        setState(() {
          _selectedVenueIndex = page;
        });

        // Fetch offers for the new venue
        final promotionState = context.read<PromotionCubit>().state;
        if (promotionState is PromotionLoaded &&
            promotionState.highlightedVenues.isNotEmpty &&
            _selectedVenueIndex < promotionState.highlightedVenues.length) {
          final currentVenue =
              promotionState.highlightedVenues[_selectedVenueIndex];

          // Only proceed if this is a different venue than before
          if (_lastFetchedVenueId != currentVenue.id) {
            // Update the last fetched venue ID
            _lastFetchedVenueId = currentVenue.id;

            // First check if we have offers in the state
            final venueOffers = promotionState.highlightedOffers
                .where((offer) => offer.venueID == currentVenue.id)
                .toList();

            if (venueOffers.isNotEmpty) {
              // Use offers from the state
              specialOffers
                ..clear()
                ..addAll(venueOffers);

              // Also update the local cache
              _offersCache[currentVenue.id] =
                  List<OfferModel>.from(venueOffers);
            }
            // Check if we have cached offers for this venue
            else if (_offersCache.containsKey(currentVenue.id) &&
                _offersCache[currentVenue.id]!.isNotEmpty) {
              // Use cached offers
              specialOffers
                ..clear()
                ..addAll(_offersCache[currentVenue.id]!);
            }
            // Fetch if we don't have cached offers and we're not already fetching
            else if (!_currentlyFetchingVenues.contains(currentVenue.id)) {
              _fetchSpecialOffersForVenue(currentVenue);
            }
          }
        }

        // Start fade in animation
        _fadeController.forward();
      });
    }
  }

  Future<void> _fetchSpecialOffersForVenue(VenueModel venue) async {
    // Skip if we're already fetching this venue
    if (_currentlyFetchingVenues.contains(venue.id)) {
      AppLogger.info(
        'Already fetching offers for ${venue.id}, skipping duplicate fetch',
      );
      return;
    }

    // Check if we already have cached offers for this venue
    if (_offersCache.containsKey(venue.id) &&
        _offersCache[venue.id]!.isNotEmpty) {
      AppLogger.info('Using cached offers for ${venue.id}');
      setState(() {
        specialOffers
          ..clear()
          ..addAll(_offersCache[venue.id]!);
      });
      return;
    }

    // Mark this venue as being fetched
    _currentlyFetchingVenues.add(venue.id);

    setState(() {
      _isFetchingOffers = true;
      // Clear existing offers to avoid duplicates
      specialOffers.clear();
    });

    try {
      // Get the promotion cubit
      final promotionCubit = context.read<PromotionCubit>();
      final user = context.read<UserBloc>().state.user;

      // Fetch offers using the cubit to store them in the state
      final offersList = await promotionCubit.fetchOffersForVenue(
        venue.id,
        user!.uid,
        user.email,
      );

      // Store in local cache as well
      _offersCache[venue.id] = List<OfferModel>.from(offersList);

      if (mounted) {
        setState(() {
          specialOffers.addAll(offersList);
          _isFetchingOffers = false;
        });
      }
    } catch (e) {
      AppLogger.error('Failed to fetch offers: $e');
    } finally {
      // Remove venue from currently fetching set
      _currentlyFetchingVenues.remove(venue.id);

      if (mounted) {
        setState(() {
          _isFetchingOffers = false;
        });
      }
    }
  }

  @override
  void dispose() {
    // Clean up controllers
    _pageController.dispose();
    _fadeController.dispose();
    _venueScrollController.dispose();
    super.dispose();
  }

  Future<void> logAnalytics(String venueID) async {
    final analytics = AnalyticsService.instance;
    await analytics.trackSponsorTileClicked(venueID);
  }

  // Track the currently selected venue index
  int _selectedVenueIndex = 0;

  // Track the last venue ID we fetched offers for
  String? _lastFetchedVenueId;

  // Controller for the PageView
  final PageController _pageController = PageController(
    viewportFraction: 0.85, // Show part of adjacent venues
    initialPage: 0,
  );

  // Animation controller for fading offers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PromotionCubit, PromotionState>(
      listener: (context, state) {
        // When the state becomes PromotionLoaded, fetch offers for the current venue
        if (state is PromotionLoaded &&
            state.highlightedVenues.isNotEmpty &&
            _selectedVenueIndex < state.highlightedVenues.length) {
          final currentVenue = state.highlightedVenues[_selectedVenueIndex];
          // Only proceed if this is a different venue than before or if specialOffers is empty
          if (_lastFetchedVenueId != currentVenue.id || specialOffers.isEmpty) {
            // Update the last fetched venue ID
            _lastFetchedVenueId = currentVenue.id;

            // First check if we have offers in the state
            final venueOffers = state.highlightedOffers
                .where((offer) => offer.venueID == currentVenue.id)
                .toList();

            if (venueOffers.isNotEmpty) {
              // Use offers from the state
              setState(() {
                specialOffers
                  ..clear()
                  ..addAll(venueOffers);
              });

              // Also update the local cache
              _offersCache[currentVenue.id] =
                  List<OfferModel>.from(venueOffers);
            }
            // Check if we have cached offers for this venue
            else if (_offersCache.containsKey(currentVenue.id) &&
                _offersCache[currentVenue.id]!.isNotEmpty) {
              // Use cached offers
              setState(() {
                specialOffers
                  ..clear()
                  ..addAll(_offersCache[currentVenue.id]!);
              });
            }
            // Fetch if we don't have cached offers and we're not already fetching
            else if (!_currentlyFetchingVenues.contains(currentVenue.id)) {
              _fetchSpecialOffersForVenue(currentVenue);
            }
          }
        }
      },
      builder: (context, state) {
        if (state is PromotionInitial) {
          context.read<PromotionCubit>().fetchHighlightedVenues();
        }
        if (state is PromotionError) {
          return const SizedBox();
        }

        // Get the current venue if available
        VenueModel? currentVenue;
        if (state is PromotionLoaded &&
            state.highlightedVenues.isNotEmpty &&
            _selectedVenueIndex < state.highlightedVenues.length) {
          currentVenue = state.highlightedVenues[_selectedVenueIndex];
        }

        return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 26,
          ),
          child: Column(
            key: const PageStorageKey('sponsor_section_tile'),
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Highlights section
              TitleWidget(
                isLoading: state is PromotionLoading,
                title: "Curator's Pick",
                exclusive: true,
              ),

              // Venue list with scroll listener to update selected venue
              SizedBox(
                height: 200,
                child: _buildVenueList(state, context),
              ),

              // Offers section for the selected venue
              if (currentVenue != null) ...[
                // Use FadeTransition for the offers section
                Container(
                  padding: const EdgeInsets.only(
                    left: 8,
                    right: 8,
                    top: 24,
                  ),
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Column(
                      children: [
                        // Title section
                        Padding(
                          padding: SizeUtils.horizontalPadding,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Special Offers',
                                    style: AppTextStyles.titleMedium,
                                  ),
                                  RichText(
                                    text: TextSpan(
                                      children: [
                                        TextSpan(
                                          text: 'From  ',
                                          style: AppTextStyles.body.copyWith(
                                            color: Colors.white
                                                .withAlpha(178), // ~70% opacity
                                            fontSize: 14,
                                          ),
                                        ),
                                        TextSpan(
                                          text: currentVenue.name,
                                          style:
                                              AppTextStyles.bodyBold.copyWith(
                                            color: Colors.white,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              GestureDetector(
                                onTap: () async {
                                  await navigateToVenueOffers(
                                    currentVenue!.id,
                                    context,
                                  );
                                },
                                child: Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withAlpha(20),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.arrow_forward,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Offers list
                        SizedBox(
                          height: 260,
                          child: _buildSpecialOffersForVenue(currentVenue),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Future<void> navigateToVenueOffers(
    String venueID,
    BuildContext context,
  ) async {
    // Get the current state of venues from the VenueBloc
    final venueState = context.read<VenueBloc>().state;
    final venues = venueState.venues;

    // Check if we already have this venue in our state with complete data
    bool venueExistsWithOffers = false;
    VenueModel? existingVenue;

    try {
      existingVenue = venues.firstWhere((venue) => venue.id == venueID);
      // Check if the venue has offers data
      venueExistsWithOffers = existingVenue.offers != null;

      // If we have the venue with offers, navigate directly
      if (venueExistsWithOffers) {
        await RouteUtils.pushNamed(
          context,
          RoutePaths.venueDescPage,
          arguments: {
            'venue': existingVenue,
            'showOffersDirectly': true,
          },
        );
        return;
      }
    } catch (e) {
      // Venue not found in the state, we'll fetch it
    }

    // If we reach here, we need to fetch the venue (either not found or missing offers)
    try {
      // Show loading indicator
      if (context.mounted) {
        unawaited(
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const Center(
              child: LoadingWidget(),
            ),
          ),
        );
      }

      // Fetch venue data
      final VenueRepository venueRepository = VenueRepository();
      final venue = await venueRepository.fetchVenueByID(
        id: venueID,
      );

      // Close loading dialog if context is still mounted
      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).pop();

        // Add venue to state if it's not already there or if it doesn't have offers

        context.read<VenueBloc>().add(
              AddVenueEvent(venue),
            );

        // Navigate to venue page
        await RouteUtils.pushNamed(
          context,
          RoutePaths.venueDescPage,
          arguments: {
            'venue': venue,
            'showOffersDirectly': true,
          },
        );
      }
    } catch (e) {
      // Close loading dialog if context is still mounted
      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load venue: $e')),
        );
      }
    }
  }

  Widget _buildVenueList(PromotionState state, BuildContext context) {
    if (state is PromotionLoading || state is PromotionInitial) {
      return ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        padding: SizeUtils.horizontalPadding,
        clipBehavior: Clip.none,
        itemBuilder: (ctx, index) => const HighlightVenueTileShimmer(),
      );
    } else if (state is PromotionError) {
      return Center(child: Text(state.message));
    } else if (state is PromotionLoaded) {
      // Use PageView for snapping carousel effect
      return Padding(
        padding: const EdgeInsets.only(left: 8),
        child: PageView.builder(
          controller: _pageController,
          itemCount: state.highlightedVenues.length,
          padEnds: false, // Don't add extra padding at the ends
          itemBuilder: (ctx, index) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: buildVenueItem(
                state.highlightedVenues[index],
                index,
                context,
                () => _handleVenueTap(
                  state.highlightedVenues[index],
                  context,
                ), // Pass the actual tap handler
              ),
            );
          },
        ),
      );
    }

    // Default empty state
    return const SizedBox();
  }

  // Build offers list for a specific venue
  Widget _buildSpecialOffersForVenue(VenueModel venue) {
    // Check if we have any offers to display
    if (specialOffers.isEmpty) {
      // If we're fetching, show loading indicator
      if (_isFetchingOffers) {
        return const LoadingWidget();
      }

      // Check if we need to trigger a fetch
      if (!_offersCache.containsKey(venue.id) &&
          !_currentlyFetchingVenues.contains(venue.id)) {
        // Schedule a fetch for the next frame to avoid setState during build
        Future.microtask(() => _fetchSpecialOffersForVenue(venue));
        return const LoadingWidget();
      }

      // If we're already fetching this venue, just show loading
      if (_currentlyFetchingVenues.contains(venue.id)) {
        return const LoadingWidget();
      }

      // If we have an empty cache entry, show the no offers message
      if (_offersCache.containsKey(venue.id) &&
          _offersCache[venue.id]!.isEmpty) {
        return Center(
          child: Padding(
            padding: SizeUtils.horizontalPadding,
            child: Text(
              'No special offers available for this venue.',
              style: AppTextStyles.body.copyWith(
                color: Colors.white.withAlpha(178),
              ),
            ),
          ),
        );
      }

      // If we're not fetching and the list is empty but we have cached offers,
      // use the cached offers
      if (_offersCache.containsKey(venue.id) &&
          _offersCache[venue.id]!.isNotEmpty) {
        // Schedule a state update for the next frame
        Future.microtask(() {
          if (mounted) {
            setState(() {
              specialOffers.addAll(_offersCache[venue.id]!);
            });
          }
        });
        return const LoadingWidget();
      }

      // Default case - show loading while we figure things out
      return const LoadingWidget();
    }

    // Generate a pseudo-random number of offers based on venue ID
    // But limit it to the actual number of offers we have
    final int maxOffers = (venue.id.hashCode % 3) + 2; // 2-4 offers per venue
    final int offerCount =
        specialOffers.length < maxOffers ? specialOffers.length : maxOffers;

    return _isFetchingOffers
        ? const LoadingWidget()
        : ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: offerCount,
            padding: SizeUtils.horizontalPadding,
            clipBehavior: Clip.none,
            itemBuilder: (ctx, index) {
              // Make sure we don't go out of bounds
              if (index >= specialOffers.length) {
                return const SizedBox(); // Return empty widget if index is out of bounds
              }

              // Create a modified offer with the current venue name
              final modifiedOffer = specialOffers[index].copyWith(
                venueName: venue.name,
              );

              return _buildSpecialOfferItem(
                modifiedOffer,
                index,
                venue.id,
              );
            },
          );
  }

  // Build individual special offer item
  Widget _buildSpecialOfferItem(OfferModel offer, int index, String venueID) {
    // Build offer card with discount tag

    return GestureDetector(
      onTap: () async {
        try {
          final AnalyticsService analytics = AnalyticsService.instance;
          await analytics.logSpecialOfferHomeInteraction(
            offerId: offer.id,
          );
        } catch (e) {
          AppLogger.error('Error logging special offer tab: $e');
        } finally {
          if (mounted) {
            await navigateToVenueOffers(venueID, context);
          }
        }
      },
      child: Container(
        margin: const EdgeInsets.only(right: 16),
        width: 180,
        decoration: BoxDecoration(
          color: darkAppColors.secondaryBackgroundColor.withAlpha(50),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withAlpha(20),
            width: 0.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image with discount tag
            Stack(
              children: [
                // Product image
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  child: CachedNetworkImage(
                    imageUrl: offer.imageLink,
                    height: 120,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Shimmer.fromColors(
                      baseColor: darkAppColors.secondaryBackgroundColor,
                      highlightColor:
                          Colors.grey.withAlpha((255 * 0.2).toInt()),
                      child: Container(
                        height: 140,
                        color: darkAppColors.secondaryBackgroundColor,
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: 140,
                      color: darkAppColors.secondaryBackgroundColor,
                      child: const Icon(Icons.error, color: Colors.white),
                    ),
                  ),
                ),

                // Discount tag
                // Positioned(
                //   top: 8,
                //   left: 8,
                //   child: Container(
                //     padding:
                //         const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                //     decoration: BoxDecoration(
                //       color: Colors.red,
                //       borderRadius: BorderRadius.circular(4),
                //     ),
                //     child: const Text(
                //       '50% off',
                //       style: TextStyle(
                //         color: Colors.white,
                //         fontWeight: FontWeight.bold,
                //         fontSize: 12,
                //       ),
                //     ),
                //   ),
                // ),
              ],
            ),

            // Product details
            Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product title
                  Text(
                    offer.title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Price information
                  Row(
                    children: [
                      // Only show original price if both values exist and are different
                      if (offer.offerValue != null &&
                          offer.discountedValue != null &&
                          offer.offerValue! > offer.discountedValue!)
                        Text(
                          '\$${offer.offerValue!.toStringAsFixed(2)}',
                          style: TextStyle(
                            decoration: TextDecoration.lineThrough,
                            color: Colors.white.withAlpha(150),
                            fontSize: 12,
                          ),
                        ),
                      if (offer.offerValue != null &&
                          offer.discountedValue != null &&
                          offer.offerValue! > offer.discountedValue!)
                        const SizedBox(width: 8),
                      // Discounted price or regular price if no discount
                      Text(
                        '${(offer.discountedValue ?? offer.offerValue ?? 0).toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  Align(
                    alignment: Alignment.bottomRight,
                    child: TextButton(
                      style: TextButton.styleFrom(
                        textStyle: AppTextStyles.body.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        padding: EdgeInsets.zero,
                      ),
                      onPressed: () async {
                        await navigateToVenueOffers(venueID, context);
                      },
                      child: const Text('View Details'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleVenueTap(VenueModel venue, BuildContext context) async {
    await logAnalytics(venue.id);

    if (venue.feeds != null && context.mounted) {
      await RouteUtils.pushNamed(
        context,
        RoutePaths.feedPlayingPage,
        arguments: {
          'feeds': venue.feeds,
          'title': 'Exclusive',
          'initialIndex': 0,
        },
      );
      return;
    }
    // Show loading indicator
    if (context.mounted) {
      unawaited(
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: LoadingWidget(),
          ),
        ),
      );
    }

    try {
      // Use a repository instance
      final feedRepo = FeedRepository();

      // Fetch feeds directly
      final feeds = await feedRepo.fetchVenueFeeds(venue.id, limit: 6);
      if (context.mounted) {
        // Update the venue in the cubit by emitting a new state
        final promotionCubit = context.read<PromotionCubit>();
        final currentVenues =
            List<VenueModel>.from(promotionCubit.state.highlightedVenues);

        // Find the index of the venue to update
        final venueIndex = currentVenues.indexWhere((v) => v.id == venue.id);
        if (venueIndex != -1) {
          // Update the venue at the found index
          currentVenues[venueIndex] = venue.copyWith(feeds: feeds);
        }

        // Update the state with the new venues list
        if (promotionCubit.state is PromotionLoaded) {
          promotionCubit.updateVenueFeeds(
            currentVenues,
            promotionCubit.state.totalCount,
          );
        }

        // Close loading dialog
        Navigator.of(context, rootNavigator: true).pop();

        // Navigate with the fetched feeds
        await RouteUtils.pushNamed(
          context,
          RoutePaths.feedPlayingPage,
          arguments: {
            'feeds': feeds,
            'title': 'Exclusive',
            'initialIndex': 0,
          },
        );
      }
    } catch (e) {
      if (context.mounted) {
        // Close loading dialog
        Navigator.of(context, rootNavigator: true).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load content: $e')),
        );
      }
    }
  }
}

class HighlightVenueTileShimmer extends StatelessWidget {
  const HighlightVenueTileShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      width: 280,
      height: 200,
      decoration: BoxDecoration(
        boxShadow: AppShadowStyles.baseStyle,
        borderRadius: BorderRadius.all(
          Radius.circular(VideoTileSizeUtils.borderRadius),
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.all(
          Radius.circular(VideoTileSizeUtils.borderRadius),
        ),
        child: RepaintBoundary(
          child: Shimmer.fromColors(
            baseColor: darkAppColors.secondaryBackgroundColor,
            highlightColor: Colors.grey.withAlpha((255 * 0.2).toInt()),
            child: Stack(
              children: [
                // Gradient overlay (similar to glassmorphic effect)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.centerRight,
                        end: Alignment.centerLeft,
                        colors: [
                          Colors.black12,
                          Colors.black12,
                          Colors.black12,
                          Colors.black.withAlpha(120),
                          Colors.black.withAlpha(160),
                        ],
                      ),
                    ),
                  ),
                ),

                // Perk badge placeholder
                Positioned(
                  top: 16,
                  left: 16,
                  child: Container(
                    width: 80,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),

                // Live indicator placeholder
                Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    width: 50,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),

                // Content placeholders
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title placeholder
                        Container(
                          width: 180,
                          height: 24,
                          color: Colors.white,
                        ),

                        const SizedBox(height: 6),

                        // Location row placeholder
                        Row(
                          children: [
                            // Location icon placeholder
                            Container(
                              width: 15,
                              height: 15,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 4),
                            // Location text placeholder
                            Container(
                              width: 80,
                              height: 15,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
