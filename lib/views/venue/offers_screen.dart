import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/components/loading_widget.dart';
import 'package:vibeo/logic/cubit/perks_cubit_cubit.dart';

import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/log/app_logger.dart';

import 'package:vibeo/widgets/dialogs/info_dialog_box.dart';
import 'package:vibeo/widgets/offer/offer_tile.dart';

class OffersSection extends StatefulWidget {
  final String venueID;
  final void Function(Widget productWidget, Offset position)? onAddToCart;

  const OffersSection({
    required this.venueID,
    this.onAddToCart,
    super.key,
  });

  @override
  State<OffersSection> createState() => _OffersSectionState();
}

class _OffersSectionState extends State<OffersSection> {
  // Lists to hold categorized offers
  final List<OfferModel> _drops = [];
  final List<OfferModel> _timeBasedOffers = [];
  final List<OfferModel> _redeemingOffers = [];
  final List<OfferModel> _expiringOffers = [];
  final List<OfferModel> _regularOffers = [];
  final List<OfferModel> _moreOffers = [];

  void _categorizeOffers(List<OfferModel> offers) {
    // Clear previous categorizations
    _drops.clear();
    _timeBasedOffers.clear();
    _redeemingOffers.clear();
    _regularOffers.clear();
    _moreOffers.clear();

    for (final offer in offers) {
      AppLogger.debug(
        'Offers: ${offer.startTime} ${offer.endTime} ${offer.weekdays}',
      );
      // Skip redeemed offers
      if (offer.isLocked) {
        _moreOffers.add(offer);
        continue;
      } else if (offer.voucherType == VoucherType.REGULAR) {
        _regularOffers.add(offer);
        continue;
      } else {
        if (offer.voucherCategory == VoucherCategory.DROPS) {
          _drops.add(offer);
          continue;
        } else if (offer.voucherCategory == VoucherCategory.ONGOING) {
          _redeemingOffers.add(offer);
          continue;
        } else if (offer.voucherCategory == VoucherCategory.TIMEBASED) {
          _timeBasedOffers.add(offer);
          continue;
        } else if (offer.voucherCategory == VoucherCategory.LIMITED) {
          _expiringOffers.add(offer);
          continue;
        }
      }
    }

    // Sort offers by time constraints, then by proximity to current time, then by priority
    _sortOffersByTimeConstraints(_drops);
    _sortOffersByTimeConstraints(_timeBasedOffers);
    _sortOffersByTimeConstraints(_redeemingOffers);
    _sortOffersByTimeConstraints(_regularOffers);
    _sortOffersByTimeConstraints(_moreOffers);
  }

  @override
  Widget build(BuildContext context) {
    final String placeIdentifier = widget.venueID;

    return BlocBuilder<PerksCubit, PerksState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: LoadingWidget());
        }

        if (!state.perks.containsKey(placeIdentifier)) {
          return const SizedBox();
        }

        // final dummyOffers = [
        //   // 1. Regular offer (just for info)
        //   OfferModel(
        //     id: '1',
        //     title: 'Happy Hour Special',
        //     description: 'Enjoy 2-for-1 drinks from 5-7pm daily',
        //     imageLink:
        //         'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
        //     redemptionType: OfferRedemptionType.regular,
        //     info: 'This offer is available to all customers during happy hour.',
        //     isRedeemed: true, // Example of a redeemed offer
        //     isLocked: false,
        //     priority: 3,
        //   ),
        //   OfferModel(
        //     id: '1',
        //     title: 'Happy Hour Special',
        //     description: 'Enjoy 2-for-1 drinks from 5-7pm daily',
        //     imageLink:
        //         'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
        //     redemptionType: OfferRedemptionType.regular,
        //     info: 'This offer is available to all customers during happy hour.',
        //     isRedeemed: false,
        //     isLocked: false,
        //   ),

        //   // 2. Free redeem offer
        //   OfferModel(
        //     id: '2',
        //     title: 'Free Welcome Drink',
        //     description:
        //         'Get a complimentary welcome drink on your first visit',
        //     imageLink:
        //         'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
        //     redemptionType: OfferRedemptionType.freeRedeem,
        //     info:
        //         'One free drink per customer. Valid for first-time visitors only.',
        //     isRedeemed: false,
        //     isLocked: false,
        //   ),

        //   // 3. VibePoints redemption offer
        //   OfferModel(
        //     id: '3',
        //     title: 'VIP Lounge Access',
        //     description: 'Redeem for exclusive access to our VIP lounge',
        //     imageLink:
        //         'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
        //     redemptionType: OfferRedemptionType.vibePointsRedeem,
        //     vibePointsCost: 100,
        //     info:
        //         'Access includes complimentary bottle service and premium seating.',
        //     isLocked: true,
        //   ),

        //   // 4. VibePoints unlock offer (locked)
        //   OfferModel(
        //     id: '4',
        //     title: 'Spend $20, Get 20% Off Tab',
        //     description:
        //         'Learn to make signature cocktails with our master mixologist',
        //     imageLink:
        //         'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
        //     redemptionType: OfferRedemptionType.freeRedeem,
        //     vibePointsCost: 200,
        //     isLocked: false,
        //     info: 'Class includes hands-on training and take-home recipe book.',
        //     showCountdown: true,
        //     expiryDate:
        //         DateTime.now().add(const Duration(days: 7)).toIso8601String(),
        //   ),

        //   // 5. Refer & unlock offer (locked)
        //   OfferModel(
        //     id: '5',
        //     title: 'Private Party Discount',
        //     description: 'Get 25% off private event bookings',
        //     imageLink:
        //         'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
        //     redemptionType: OfferRedemptionType.referUnlock,
        //     requiredReferrals: 3,
        //     isLocked: true,
        //     info:
        //         'Valid for events with 10+ guests. Booking must be made 2 weeks in advance.',
        //   ),
        // ];

        // final dummyOffers = [
        //   // 1. Spend $20, Get 20% Off Tab - Always-On - Suggested
        //   OfferModel(
        //     id: '1',
        //     venueName: 'Replay Lincoln Park',
        //     title: r'Spend $20, Get 20% Off Tab',
        //     description: r'Get 20% off your tab when you spend $20 or more',
        //     imageLink:
        //         'https://images.unsplash.com/photo-1547595628-c61a29f496f0?q=80&w=3087&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        //     redemptionType: OfferRedemptionType.freeRedeem,
        //     info:
        //         'If there’s an issue, tap ‘Get Help’ on the top right. If everything looks good, hand the phone back to the guest. The guest can tap done.',
        //     isRedeemed: false,
        //     isLocked: false,
        //     priority: 1,
        //     startTime: '',
        //     endTime: '',
        //     weekdays: [],
        //     offerType: 'Always-On',
        //     showCountdown: true,
        //   ),

        //   // 2. Buy 1 Shrek Cocktail, Get 2nd Discount - Flexible timing - Confirmed
        //   OfferModel(
        //     id: '2',
        //     venueName: 'Replay Lincoln Park',
        //     title: 'Buy 1 Shrek Cocktail, Get 2nd Discount',
        //     description:
        //         'Purchase one Shrek-themed cocktail and get a discount on your second',
        //     imageLink:
        //         'https://images.unsplash.com/photo-1607446045926-3aee01b43c17?q=80&w=2606&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        //     redemptionType: OfferRedemptionType.freeRedeem,
        //     info:
        //         'If there’s an issue, tap ‘Get Help’ on the top right. If everything looks good, hand the phone back to the guest. The guest can tap done.',
        //     isRedeemed: false,
        //     isLocked: false,
        //     priority: 2,
        //     startTime: '',
        //     endTime: '',
        //     weekdays: [],
        //     offerType: 'Always-On',
        //     showCountdown: true,
        //   ),

        //   // 3. $5 Craft Beers - Friday Only, 4-7 PM - Confirmed
        //   OfferModel(
        //     id: '3',
        //     venueName: 'Replay Lincoln Park',
        //     title: r'$5 Craft Beers',
        //     description:
        //         r'Enjoy craft beers for just $5 during Friday happy hour',
        //     imageLink:
        //         'https://images.unsplash.com/photo-1608270586620-248524c67de9?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        //     redemptionType: OfferRedemptionType.freeRedeem,
        //     info:
        //         'If there’s an issue, tap ‘Get Help’ on the top right. If everything looks good, hand the phone back to the guest. The guest can tap done.',
        //     isRedeemed: false,
        //     isLocked: false,
        //     priority: 3,
        //     startTime: '16:00',
        //     endTime: '19:00',
        //     weekdays: ['Friday'],
        //     offerType: 'Time-Based',
        //     showCountdown: true,
        //   ),

        //   // 4. 50% Off Shrek Cocktails - Thursday Only, 8-10 PM - Confirmed
        //   OfferModel(
        //     id: '4',
        //     venueName: 'Replay Lincoln Park',
        //     title: '50% Off Shrek Cocktails',
        //     description: 'Half price on all Shrek-themed specialty cocktails',
        //     imageLink:
        //         'https://images.unsplash.com/photo-1563223771-375783ee91ad?q=80&w=3087&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        //     redemptionType: OfferRedemptionType.freeRedeem,
        //     info:
        //         'If there’s an issue, tap ‘Get Help’ on the top right. If everything looks good, hand the phone back to the guest. The guest can tap done.',
        //     isRedeemed: false,
        //     isLocked: false,
        //     priority: 4,
        //     startTime: '20:00',
        //     endTime: '22:00',
        //     weekdays: ['Thursday'],
        //     offerType: 'Time-Based',
        //     showCountdown: true,
        //   ),

        //   // 5. Express Entry via Vibeo App - Dropped - Confirmed
        //   OfferModel(
        //     id: '5',
        //     venueName: 'Replay Lincoln Park',
        //     title: 'Express Entry via Vibeo App',
        //     description:
        //         'Skip the line with express entry when you show the Vibeo app',
        //     imageLink:
        //         'https://images.unsplash.com/photo-1630340338979-721d11f8cf26?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        //     redemptionType: OfferRedemptionType.freeRedeem,
        //     info:
        //         'If there’s an issue, tap ‘Get Help’ on the top right. If everything looks good, hand the phone back to the guest. The guest can tap done.',
        //     isRedeemed: false,
        //     isLocked: false,
        //     priority: 5,
        //     startTime: '',
        //     endTime: '',
        //     weekdays: [],
        //     offerType: 'Always-On',
        //     showCountdown: true,
        //   ),

        //   // 6. Secret Cocktail Drop (App Only) - Dropped - To Be Discussed
        //   OfferModel(
        //     id: '6',
        //     venueName: 'Replay Lincoln Park',
        //     title: 'Secret Cocktail Drop (App Only)',
        //     description:
        //         'Exclusive access to a secret cocktail not on the menu',
        //     imageLink:
        //         'https://images.unsplash.com/photo-1468072114808-903e572b8ead?q=80&w=3173&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        //     redemptionType: OfferRedemptionType.freeRedeem,
        //     info:
        //         'If there’s an issue, tap ‘Get Help’ on the top right. If everything looks good, hand the phone back to the guest. The guest can tap done.',
        //     isRedeemed: false,
        //     isLocked: false,
        //     priority: 6,
        //     startTime: '',
        //     endTime: '',
        //     weekdays: [],
        //     offerType: 'Dropped',
        //     showCountdown: true,
        //   ),

        //   OfferModel(
        //     id: '3',
        //     venueName: 'Replay Lincoln Park',
        //     title: 'VIP Lounge Access',
        //     description: 'Redeem for exclusive access to our VIP lounge',
        //     imageLink:
        //         'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
        //     redemptionType: OfferRedemptionType.vibePointsRedeem,
        //     vibePointsCost: 100,
        //     info:
        //         'If there’s an issue, tap ‘Get Help’ on the top right. If everything looks good, hand the phone back to the guest. The guest can tap done.',
        //     isLocked: true,
        //   ),
        // ];

        final List<OfferModel> offers =
            state.perks[placeIdentifier]!['offers'] as List<OfferModel>;
        final newOffers = offers;
        if (newOffers.isEmpty) {
          return const SizedBox();
        }

        // Categorize offers
        _categorizeOffers(newOffers);

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Offers', style: AppTextStyles.title),
                    GestureDetector(
                      onTap: () {
                        InfoDialog.showInfo(
                          context,
                          title: 'Info',
                          description:
                              'Offers may vary. Please confirm with the venue before visiting.',
                        );
                      },
                      child: const Icon(
                        CupertinoIcons.info_circle,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),

              // Drops section
              if (_drops.isNotEmpty)
                _buildSection(
                  'Drops',
                  _drops,
                  Icons.flash_on,
                  [Colors.purple.shade400, Colors.deepPurple.shade600],
                ),

              // Time-based Offers section
              if (_timeBasedOffers.isNotEmpty)
                _buildSection(
                  'Limited-Time Perks',
                  _timeBasedOffers,
                  CupertinoIcons.time,
                  [Colors.orange.shade400, Colors.deepOrange.shade600],
                ),

              // Redeeming Offers section
              if (_redeemingOffers.isNotEmpty)
                _buildSection(
                  'Ongoing Deals',
                  _redeemingOffers,
                  CupertinoIcons.gift,
                  [Colors.green.shade400, Colors.teal.shade600],
                ),

              // Regular Offers section
              if (_regularOffers.isNotEmpty)
                _buildSection(
                  'Spotted Deals',
                  _regularOffers,
                  CupertinoIcons.tag,
                  [Colors.blue.shade400, Colors.indigo.shade600],
                ),

              // Locked Offers section
              if (_moreOffers.isNotEmpty)
                _buildSection(
                  'Unlockable Offers',
                  _moreOffers,
                  CupertinoIcons.lock,
                  [Colors.pink.shade400, Colors.red.shade600],
                ),
            ],
          ),
        );
      },
    );
  }

  // Helper method to sort offers by time constraints and proximity to current time
  void _sortOffersByTimeConstraints(List<OfferModel> offers) {
    // Sort by priority first, then by time constraints
    offers
      ..sort((a, b) {
        // Check if offers have time constraints
        final aHasTimeConstraints =
            (a.startTime.isNotEmpty && a.endTime.isNotEmpty) ||
                a.weekdays.isNotEmpty;
        final bHasTimeConstraints =
            (b.startTime.isNotEmpty && b.endTime.isNotEmpty) ||
                b.weekdays.isNotEmpty;

        // If both have or both don't have time constraints, they're equal in this sort
        if (aHasTimeConstraints == bHasTimeConstraints) {
          // If both have time constraints, sort by proximity to current time
          if (aHasTimeConstraints) {
            return _getTimeProximityScore(a)
                .compareTo(_getTimeProximityScore(b));
          }
          return 0; // Both don't have time constraints, keep original order
        }

        // Offers with time constraints come first (return -1)
        return aHasTimeConstraints ? -1 : 1;
      })
      ..sort((a, b) => a.priority.compareTo(b.priority));
  }

  // Calculate a score representing how close an offer is to the current time
  // Lower score means closer to current time
  int _getTimeProximityScore(OfferModel offer) {
    // If no time constraints, return a high score
    if ((offer.startTime.isEmpty || offer.endTime.isEmpty) &&
        offer.weekdays.isEmpty) {
      return 1000000;
    }

    final now = DateTime.now();
    int score = 0;

    // Check weekday proximity
    if (offer.weekdays.isNotEmpty) {
      final currentWeekday = _getCurrentWeekday(now);
      if (offer.weekdays.contains(currentWeekday)) {
        // Current weekday matches, lowest score
        score += 0;
      } else {
        // Calculate days until next valid weekday
        final weekdayOrder = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday',
        ];
        final currentWeekdayIndex = weekdayOrder.indexOf(currentWeekday);

        int minDaysAway = 7; // Maximum days away
        for (final weekday in offer.weekdays) {
          final offerWeekdayIndex = weekdayOrder.indexOf(weekday);
          final int daysAway = (offerWeekdayIndex - currentWeekdayIndex) % 7;
          if (daysAway < minDaysAway) {
            minDaysAway = daysAway;
          }
        }
        score += minDaysAway * 1440; // Convert days to minutes (24*60)
      }
    }

    // Check time proximity if we have time constraints
    if (offer.startTime.isNotEmpty && offer.endTime.isNotEmpty) {
      // Parse start and end times
      final startTimeParts = offer.startTime.split(':');
      final endTimeParts = offer.endTime.split(':');

      if (startTimeParts.length >= 2 && endTimeParts.length >= 2) {
        // Extract hours and minutes, handling AM/PM format
        int startHour = int.parse(startTimeParts[0]);
        final int startMinute = int.parse(startTimeParts[1].split(' ')[0]);
        final startPeriod = offer.startTime.contains('PM') ? 'PM' : 'AM';

        // Convert to 24-hour format
        if (startPeriod == 'PM' && startHour < 12) {
          startHour += 12;
        }
        if (startPeriod == 'AM' && startHour == 12) {
          startHour = 0;
        }

        // Calculate minutes from midnight
        final startTimeMinutes = startHour * 60 + startMinute;
        final currentTimeMinutes = now.hour * 60 + now.minute;

        // Calculate minutes until start time
        int minutesUntilStart = (startTimeMinutes - currentTimeMinutes) % 1440;
        if (minutesUntilStart < 0) {
          minutesUntilStart += 1440; // Add a day if negative
        }

        // Add to score
        score += minutesUntilStart;
      }
    }

    return score;
  }

  // Helper to get current weekday as string
  String _getCurrentWeekday(DateTime date) {
    switch (date.weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return '';
    }
  }

  Widget _buildSection(
    String title,
    List<OfferModel> offers,
    IconData icon,
    List<Color> gradientColors,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with glassmorphic design and animation
        Container(
          margin: const EdgeInsets.only(bottom: 16, top: 8),

          // decoration: BoxDecoration(
          //   borderRadius: BorderRadius.circular(16),
          //   border: Border.all(
          //     color: gradientColors[0].withAlpha(76),
          //     width: 1,
          //   ),
          //   boxShadow: [
          //     BoxShadow(
          //       color: gradientColors[0].withAlpha(30),
          //       blurRadius: 8,
          //       offset: const Offset(0, 2),
          //     ),
          //   ],
          // ),
          child: Row(
            children: [
              // Icon with gradient background
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradientColors,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: gradientColors[1].withAlpha(40),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: AppTextStyles.body.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
              const Spacer(),
              // Count badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: gradientColors[1].withAlpha(51),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: gradientColors[1].withAlpha(102),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${offers.length}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Offers list
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.only(top: 4, bottom: 10),
          itemCount: offers.length,
          itemBuilder: (ctx, index) => OfferTile(
            offer: offers[index],
            canNavigate: false,
            canRedeem: false,
            onAddToCart: widget.onAddToCart,
            canAddToCart: !offers[index].isLocked,
          ),
        ),

        const SizedBox(
          height: 24,
        ),
      ],
    );
  }
}
