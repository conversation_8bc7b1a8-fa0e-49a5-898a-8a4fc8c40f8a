import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:vibeo/components/component.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/models/offer/redeemed_offer_data.dart';
import 'package:vibeo/logic/redemption/repository/redeemed_orders_repository.dart';

import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

class EarnedOffersPage extends StatefulWidget {
  final String searchQuery;

  const EarnedOffersPage({
    this.searchQuery = '',
    super.key,
  });

  @override
  State<EarnedOffersPage> createState() => _EarnedOffersPageState();
}

class _EarnedOffersPageState extends State<EarnedOffersPage> {
  List<VenueRedeemedOffers> _venueOffers = [];
  final RedeemedOrdersRepository _repository = RedeemedOrdersRepository();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadRedeemedOffers();
  }

  @override
  void didUpdateWidget(EarnedOffersPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchQuery != widget.searchQuery) {
      _filterOffers();
    }
  }

  Future<void> _loadRedeemedOffers() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // In a real app, you would get these values from user authentication
      // For now, we'll use the test values from the API example
      final user = context.read<UserBloc>().state.user;
      final userId = user!.uid;
      final userEmail = user.email;

      final venueOffers = await _repository.fetchRedeemedOrders(
        userId: userId,
        userEmail: userEmail,
      );

      setState(() {
        _venueOffers = venueOffers;
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error('Error loading redeemed offers: $e');
      setState(() {
        _errorMessage = 'Failed to load redeemed offers. Please try again.';
        _isLoading = false;
      });
    }
  }

  void _filterOffers() {
    if (widget.searchQuery.isEmpty) {
      _loadRedeemedOffers();
      return;
    }

    // Load all offers first if empty
    if (_venueOffers.isEmpty) {
      _loadRedeemedOffers();
    }

    // Filter the existing offers
    final filteredVenueOffers = _venueOffers.where((venueOffer) {
      // Check if venue name matches
      if (venueOffer.venueName
          .toLowerCase()
          .contains(widget.searchQuery.toLowerCase())) {
        return true;
      }

      // Check if any offer title or description matches
      return venueOffer.offers.any(
        (offer) =>
            offer.offer.title
                .toLowerCase()
                .contains(widget.searchQuery.toLowerCase()) ||
            offer.offer.description
                .toLowerCase()
                .contains(widget.searchQuery.toLowerCase()),
      );
    }).toList();

    setState(() {
      _venueOffers = filteredVenueOffers;
    });
  }

  @override
  Widget build(BuildContext context) {
    AppLogger.info(
      'Building EarnedOffersPage with search query: $_venueOffers',
    );
    return RefreshIndicator(
      onRefresh: () async {
        await _loadRedeemedOffers();
      },
      child: _isLoading
          ? _buildLoadingState()
          : _errorMessage != null
              ? _buildErrorState()
              : _venueOffers.isEmpty
                  ? _buildEmptyState()
                  : ListView(
                      padding: EdgeInsets.only(
                        top: 24,
                        bottom: 60,
                        left: SizeUtils.horizontalPadding.left,
                        right: SizeUtils.horizontalPadding.right,
                      ),
                      children: [
                        // List of venues with their offers
                        ...List.generate(_venueOffers.length, (index) {
                          return _buildVenueOffersCard(_venueOffers[index]);
                        }),
                      ],
                    ),
    );
  }

  Widget _buildLoadingState() {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.5,
          child: const Center(
            child: LoadingWidget(),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState() {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.5,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  CupertinoIcons.exclamationmark_circle,
                  size: 64,
                  color: Color.fromRGBO(255, 255, 255, 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  _errorMessage ?? 'An error occurred',
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: const Color.fromRGBO(255, 255, 255, 0.7),
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _loadRedeemedOffers,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: darkAppColors.deepPurple,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: Text(
                    'Try Again',
                    style: AppTextStyles.bodysmallBold.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: 70,
      height: 70,
      color: Colors.grey.shade800,
      child: const Icon(
        Icons.image_not_supported,
        color: Colors.white54,
      ),
    );
  }

  Widget _buildVenueOffersCard(VenueRedeemedOffers venueOffers) {
    // Debug print
    AppLogger.info(
      'Building venue card for ${venueOffers.venueName} with ${venueOffers.offers.length} offers',
    );
    for (final offer in venueOffers.offers) {
      AppLogger.info('  - Offer: ${offer.offer.title}');
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: darkAppColors.secondaryBackgroundColor.withAlpha(128),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withAlpha(25),
          width: 0.5,
        ),
        // Add subtle glassmorphism effect
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Venue header with name and redemption count
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        venueOffers.venueName,
                        style: AppTextStyles.title,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // Redemption count badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: darkAppColors.deepPurple.withAlpha(51),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: darkAppColors.deepPurple.withAlpha(77),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        '${venueOffers.offers.length} ${venueOffers.offers.length == 1 ? 'offer' : 'offers'}',
                        style: AppTextStyles.bodySmaller.copyWith(
                          color: Colors.white.withAlpha(230),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),

                // Latest redemption date
                // if (venueOffers.latestRedemptionDate.isNotEmpty)
                //   Padding(
                //     padding: const EdgeInsets.only(top: 8),
                //     child: Row(
                //       children: [
                //         Icon(
                //           CupertinoIcons.calendar,
                //           color: Colors.white.withAlpha(179),
                //           size: 14,
                //         ),
                //         const SizedBox(width: 4),
                //         Text(
                //           'Last redeemed: ${_formatDate(venueOffers.latestRedemptionDate)}',
                //           style: AppTextStyles.bodySmaller.copyWith(
                //             color: Colors.white.withAlpha(179),
                //           ),
                //         ),
                //       ],
                //     ),
                //   ),
              ],
            ),
          ),

          // Divider
          Divider(
            color: Colors.white.withAlpha(25),
            height: 1,
          ),

          // List of offers for this venue
          ...List.generate(venueOffers.offers.length, (index) {
            return _buildRedeemedOfferTile(
              venueOffers.offers[index].offer,
              venueOffers.offers[index].vibePointsEarned,
              venueOffers.offers[index].redemptionDate,
            );
          }),
        ],
      ),
    );
  }

  // Helper method to format dates consistently
  String _formatDate(String dateString) {
    DateTime? parsedDate;
    try {
      parsedDate = DateTime.parse(dateString);
      return DateFormat('MMMM d, yyyy').format(parsedDate);
    } catch (e) {
      AppLogger.error('Error parsing date: $dateString - $e');
      return dateString;
    }
  }

  Widget _buildRedeemedOfferTile(
    OfferModel offer,
    int vibePointsEarned, // VIBEPOINTS FEATURE COMMENTED OUT - parameter kept for compatibility
    String latestRedemptionDate,
  ) {
    // Parse the date from the API format (YYYY-MM-DD)
    DateTime? parsedDate;
    try {
      parsedDate = DateTime.parse(latestRedemptionDate);
    } catch (e) {
      AppLogger.error('Error parsing date: $latestRedemptionDate - $e');
    }

    final redemptionDate = parsedDate != null
        ? DateFormat('MMMM d, yyyy').format(parsedDate)
        : latestRedemptionDate;

    // Debug print
    AppLogger.info(
      'Building offer tile: ${offer.title} for venue: ${offer.venueName}',
    );

    return HapticButton(
      onTap: () {
        // Navigator.of(context).push(
        //   MaterialPageRoute(
        //     builder: (context) => RedeemedOfferDetailsPage(
        //       offer: offer,
        //       venueID: offer.venueID,
        //     ),
        //   ),
        // );
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.white.withAlpha(15),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Offer image with improved error handling
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: offer.imageLink.isNotEmpty
                  ? Image.network(
                      offer.imageLink,
                      width: 70,
                      height: 70,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        AppLogger.error(
                          'Error loading image: ${offer.imageLink} - $error',
                        );
                        return _buildPlaceholderImage();
                      },
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          width: 70,
                          height: 70,
                          color: Colors.grey.shade800,
                          child: Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                              strokeWidth: 2,
                              color: Colors.white70,
                            ),
                          ),
                        );
                      },
                    )
                  : _buildPlaceholderImage(),
            ),
            const SizedBox(width: 12),

            // Offer details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    offer.title,
                    style: AppTextStyles.bodysmallBold,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    offer.description,
                    style: AppTextStyles.bodySmaller.copyWith(
                      color: Colors.white.withAlpha(179),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),

                  // Bottom row with redeemed status and date
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Redeemed status
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.withAlpha(51),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              CupertinoIcons.checkmark_circle_fill,
                              color: Colors.green,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Redeemed',
                              style: AppTextStyles.bodySmaller.copyWith(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Redemption date
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Text(
                          redemptionDate,
                          style: AppTextStyles.bodySmaller.copyWith(
                            color: Colors.white.withAlpha(153),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.5,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  CupertinoIcons.ticket,
                  size: 64,
                  color: Color.fromRGBO(255, 255, 255, 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  widget.searchQuery.isEmpty
                      ? 'No redeemed offers yet'
                      : 'No redeemed offers match your search',
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: const Color.fromRGBO(255, 255, 255, 0.7),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  widget.searchQuery.isEmpty
                      ? 'When you redeem offers, they will appear here'
                      : 'Try a different search term',
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodySmaller.copyWith(
                    color: const Color.fromRGBO(255, 255, 255, 0.5),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
