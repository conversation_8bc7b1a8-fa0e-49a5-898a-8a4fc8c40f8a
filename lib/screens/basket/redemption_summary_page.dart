import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:vibeo/logic/cart/bloc/cart_bloc.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/services/basket/basket_storage_service.dart';
import 'package:vibeo/services/redeemed_offers/redeemed_offers_service.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

class RedemptionSummaryPage extends StatefulWidget {
  final OfferModel offer;
  final String venueID;
  final bool showDoneButton;

  final RedemptionConfirmation confirmation;

  const RedemptionSummaryPage({
    required this.offer,
    required this.venueID,
    required this.confirmation,
    this.showDoneButton = true,
    super.key,
  });

  @override
  State<RedemptionSummaryPage> createState() => _RedemptionSummaryPageState();
}

class _RedemptionSummaryPageState extends State<RedemptionSummaryPage>
    with SingleTickerProviderStateMixin {
  late String _redemptionCode;
  late String _redemptionDate;
  late String _userId;
  late RedeemedOffersService _redeemedOffersService;
  late BasketStorageService _basketStorageService;
  late int _quantity = 1; // Default quantity
  late OfferModel _redeemedOffer;

  @override
  void initState() {
    super.initState();
    _userId = context.read<UserBloc>().state.user!.uid;
    _redemptionCode = widget.confirmation.redemptionId;
    _redemptionDate = DateFormat("MMMM d, yyyy 'at' h:mm a").format(
      DateTime.parse(
        widget.confirmation.transactionConfirmationTime,
      ),
    );
    _quantity = widget.confirmation.quantity;

    // Create a copy of the offer with isRedeemed set to true
    _redeemedOffer = widget.offer.copyWith(isRedeemedToday: true);

    // Initialize services
    _redeemedOffersService = RedeemedOffersService(_userId);
    _basketStorageService = BasketStorageService(_userId);

    // Save this offer as redeemed and remove from basket
    _saveRedeemedOffer();
    _removeFromBasket();
  }

  // Remove the redeemed offer from the basket
  Future<void> _removeFromBasket() async {
    try {
      // Remove from basket storage
      await _basketStorageService.removeOfferFromBasket(
        widget.venueID,
        widget.offer.id,
      );

      // Remove from cart bloc if it exists
      if (mounted) {
        final cartBloc = context.read<CartBloc>();
        if (cartBloc.state.cart.containsKey(widget.venueID)) {
          cartBloc.add(
            RemoveOfferFromCart(
              venueId: widget.venueID,
              offerId: widget.offer.id,
            ),
          );
        }
      }
    } catch (e) {
      // Log error but continue - this is not critical
      debugPrint('Error removing offer from basket: $e');
    }
  }

  Future<void> _saveRedeemedOffer() async {
    // Save to storage with vibe points earned
    await _redeemedOffersService.saveRedeemedOffer(
      _redeemedOffer,
      widget.venueID,
      _redemptionCode,
    );
  }

  // Show congratulations dialog with two buttons
  void _showCongratulationsDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withAlpha(178), // ~0.7 opacity
      builder: (context) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(28),
                decoration: BoxDecoration(
                  color: darkAppColors.secondaryBackgroundColor.withAlpha(180),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: darkAppColors.deepPurple.withAlpha(100),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Sparkle animation with deep purple color
                    TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0, end: 1),
                      duration: const Duration(milliseconds: 800),
                      curve: Curves.easeOutBack,
                      builder: (context, value, child) {
                        return Transform.scale(
                          scale: value,
                          child: child,
                        );
                      },
                      child: const Icon(
                        CupertinoIcons.checkmark_circle_fill,
                        color: Colors.green,
                        size: 70,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Congratulations text
                    Text(
                      'Congratulations!',
                      style: AppTextStyles.title.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Success message with animation
                    TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0, end: 1),
                      duration: const Duration(milliseconds: 1000),
                      curve: Curves.easeOut,
                      builder: (context, value, child) {
                        return Opacity(
                          opacity: value,
                          child: Transform.scale(
                            scale: value,
                            child: child,
                          ),
                        );
                      },
                      child: Text(
                        "You've successfully redeemed your offer!",
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Explore More Offers button
                    // HapticButton(
                    //   onTap: () {
                    //     Navigator.pop(context); // Close dialog

                    //     // Navigate to venue description page with showOffersDirect=true
                    //     Navigator.pushNamedAndRemoveUntil(
                    //       context,
                    //       RoutePaths.venueDescPage,
                    //       (route) => false,
                    //       arguments: {
                    //         'venueID': widget.venueID,
                    //         'showOffersDirect': true,
                    //       },
                    //     );
                    //   },
                    //   child: Container(
                    //     width: double.infinity,
                    //     height: 50,
                    //     decoration: BoxDecoration(
                    //       color: darkAppColors.secondaryBackgroundColor,
                    //       borderRadius: BorderRadius.circular(12),
                    //       border: Border.all(
                    //         color: Colors.white.withAlpha(30),
                    //         width: 1,
                    //       ),
                    //     ),
                    //     child: Center(
                    //       child: Text(
                    //         'Explore More Offers',
                    //         style: AppTextStyles.bodysmallBold.copyWith(
                    //           color: Colors.white,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    // const SizedBox(height: 16),

                    // Back to Homepage button
                    HapticButton(
                      onTap: () {
                        Navigator.pop(context); // Close dialog

                        // Navigate to home page
                        RouteUtils.pushNamedAndRemoveUntil(
                          context,
                          RoutePaths.home,
                          (route) => false,
                        );
                      },
                      child: Container(
                        width: double.infinity,
                        height: 50,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Text(
                            'Back to Homepage',
                            style: AppTextStyles.bodysmallBold.copyWith(
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkAppColors.backgroundColor,
      appBar: const CupertinoNavigationBar(
        backgroundColor: Colors.transparent,
        middle: Text(
          'Redemption Summary',
          style: TextStyle(
            color: Colors.white,
          ),
        ),
        automaticallyImplyLeading: false,
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: widget.showDoneButton
          ? Padding(
              padding: SizeUtils.horizontalPadding,
              child: HapticButton(
                onTap: _showCongratulationsDialog,
                child: Container(
                  width: double.infinity,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      'Done',
                      style: AppTextStyles.bodysmallBold.copyWith(
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
              ),
            )
          : null,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: SizeUtils.horizontalPadding.copyWith(
            bottom: 100,
            top: 12,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Success indicator
              const Icon(
                CupertinoIcons.checkmark_circle_fill,
                color: Colors.green,
                size: 60,
              ),
              const SizedBox(height: 16),

              Text(
                'Offer Successfully Redeemed!',
                style: AppTextStyles.title,
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Offer title - Prominently displayed for bartenders
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: darkAppColors.deepPurple,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: darkAppColors.deepPurple.withAlpha(80),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'OFFER',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white70,
                        letterSpacing: 1.5,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.offer.title,
                      style: AppTextStyles.heading1.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 32,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.offer.venueName,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white.withAlpha(200),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    // const SizedBox(height: 8),
                    // Text(
                    //   widget.offer.description,
                    //   style: AppTextStyles.heading2.copyWith(
                    //     color: Colors.white,
                    //   ),
                    //   textAlign: TextAlign.center,
                    // ),
                    // if (widget.offer.info != null) ...[
                    //   const SizedBox(height: 8),
                    //   Text(
                    //     widget.offer.info!,
                    //     style: AppTextStyles.bodySmall.copyWith(
                    //       color: Colors.white.withAlpha(200),
                    //     ),
                    //     textAlign: TextAlign.center,
                    //   ),
                    // ],
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Redemption code
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(40),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      'REDEMPTION CODE',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white70,
                        letterSpacing: 1.5,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _redemptionCode,
                      style: AppTextStyles.headingTitle.copyWith(
                        letterSpacing: 2,
                        fontWeight: FontWeight.bold,
                        fontSize: 32,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Redeemed on $_redemptionDate',
                      style: AppTextStyles.bodySmaller.copyWith(
                        color: Colors.white60,
                      ),
                    ),
                  ],
                ),
              ),

              const Padding(
                padding: EdgeInsets.symmetric(vertical: 20),
                child: Divider(color: Colors.white24),
              ),

              // Order details section
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Order Details',
                  style: AppTextStyles.bodysmallBold.copyWith(fontSize: 18),
                ),
              ),

              const SizedBox(height: 16),

              _buildDetailRow('Venue', widget.offer.venueName),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 8),
                child: Divider(color: Colors.white12, height: 1),
              ),

              _buildDetailRow('Item', widget.offer.title),

              _buildDetailRow('Quantity', '$_quantity'),

              const Padding(
                padding: EdgeInsets.symmetric(vertical: 20),
                child: Divider(color: Colors.white24),
              ),

              // Price section
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Price Details',
                  style: AppTextStyles.bodysmallBold.copyWith(fontSize: 18),
                ),
              ),

              const SizedBox(height: 16),
              if (widget.offer.discountedValue == null &&
                  widget.offer.offerValue == null) ...[
                Text(
                  'Price: Not Available',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white70,
                  ),
                ),
              ] else ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Original Price',
                      style: AppTextStyles.bodySmall
                          .copyWith(color: Colors.white70),
                    ),
                    if (widget.offer.discountedValue! > 0 &&
                        widget.offer.offerValue! > 0)
                      Text(
                        '\$${widget.offer.offerValue}',
                        style: AppTextStyles.bodySmall.copyWith(
                          decoration: TextDecoration.lineThrough,
                          color: Colors.white60,
                        ),
                      )
                    else
                      Text(
                        '\$${widget.offer.offerValue}',
                        style: AppTextStyles.bodySmall,
                      ),
                  ],
                ),
              ],

              const SizedBox(height: 8),
              if (widget.offer.discountedValue != null &&
                  widget.offer.offerValue != 0) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Discount',
                      style: AppTextStyles.bodySmall
                          .copyWith(color: Colors.white70),
                    ),
                    Text(
                      '-\$${widget.offer.offerValue! - double.parse(widget.offer.discountedValue.toString())}',
                      style:
                          AppTextStyles.bodySmall.copyWith(color: Colors.green),
                    ),
                  ],
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8),
                  child: Divider(color: Colors.white12, height: 1),
                ),
              ],
              if (widget.offer.discountedValue != null) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Final Price',
                      style: AppTextStyles.bodysmallBold,
                    ),
                    Text(
                      '\$${widget.offer.discountedValue}',
                      style: AppTextStyles.bodysmallBold.copyWith(
                        color: Colors.green,
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total (Qty: $_quantity)',
                      style: AppTextStyles.bodysmallBold,
                    ),
                    Text(
                      '${widget.offer.discountedValue}',
                      style: AppTextStyles.bodysmallBold.copyWith(
                        color: Colors.green,
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
              ],

              // const Padding(
              //   padding: EdgeInsets.symmetric(vertical: 20),
              //   child: Divider(color: Colors.white24),
              // ),

              // Instructions section
              // Container(
              //   padding: const EdgeInsets.all(16),
              //   decoration: BoxDecoration(
              //     color: Colors.amber.withAlpha(25),
              //     borderRadius: BorderRadius.circular(12),
              //     border: Border.all(
              //       color: Colors.amber.withAlpha(77),
              //       width: 1,
              //     ),
              //   ),
              //   child: Column(
              //     children: [
              //       Row(
              //         children: [
              //           const Icon(
              //             CupertinoIcons.info_circle_fill,
              //             color: Colors.amber,
              //             size: 24,
              //           ),
              //           const SizedBox(width: 12),
              //           Expanded(
              //             child: Text(
              //               'INSTRUCTIONS FOR REDEMPTION',
              //               style: AppTextStyles.bodysmallBold.copyWith(
              //                 color: Colors.amber,
              //                 letterSpacing: 1,
              //               ),
              //             ),
              //           ),
              //         ],
              //       ),
              //       const SizedBox(height: 12),
              //       Container(
              //         width: double.infinity,
              //         padding: const EdgeInsets.all(12),
              //         decoration: BoxDecoration(
              //           color: Colors.black.withAlpha(40),
              //           borderRadius: BorderRadius.circular(8),
              //         ),
              //         child: Text(
              //           'Show this screen to the bartender or staff member to claim your offer. They will verify the redemption code and provide your offer.',
              //           style: AppTextStyles.bodySmall.copyWith(
              //             color: Colors.white,
              //             height: 1.4,
              //           ),
              //         ),
              //       ),
              //     ],
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
          ),
          const SizedBox(width: 16),
          Flexible(
            child: Text(
              value,
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}
