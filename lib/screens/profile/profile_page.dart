import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:vibeo/analytics/analytics.dart';

import 'package:vibeo/helper/open_web_link.dart';
import 'package:vibeo/logic/auth/bloc/auth_bloc.dart';
import 'package:vibeo/logic/feed/controller/feed_controller.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/user/user_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/auth/dialogs/faq_dialog.dart';
import 'package:vibeo/widgets/auth/dialogs/privacy_policy_dialog.dart';
import 'package:vibeo/widgets/auth/dialogs/t_c_profile_dialog.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  late final UserModel? user;
  late FeedController feedController;

  @override
  void initState() {
    user = context.read<UserBloc>().state.user;
    feedController = FeedController(user!.uid);
    super.initState();
  }

  List<Map<String, String>> forYouSection = [
    {
      'title': 'Saved Venues',
      'pageID': RoutePaths.savedVenuesPage,
    },
  ];

  List<Map<String, String>> yourActivitySection = [
    {
      'title': 'Your Vibes',
      'description': 'View all your created vibes',
      'pageID': RoutePaths.profileVibesPage,
    },
    {
      'title': 'Vibes Liked',
      'description': "See the vibes you've liked",
      'pageID': RoutePaths.vibesLikedPage,
    },
    // VIBEPOINTS FEATURE COMMENTED OUT
    // {
    //   'title': 'Your Vibe Points Activity',
    //   'description': 'Track your vibe points history',
    //   'pageID': RoutePaths.vibePointsActivityPage,
    // },
  ];

  Future<Map<String, int>> _loadVibesInfo() async {
    final vibesWatched = await feedController.vibesWatched();
    final vibesLiked = await feedController.vibesLiked();
    return {
      'watched': vibesWatched,
      'liked': vibesLiked,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkAppColors.backgroundColor,
      appBar: CupertinoNavigationBar(
        backgroundColor: Colors.transparent,
        automaticallyImplyLeading: false,
        trailing: IconButton(
          iconSize: 24,
          onPressed: () async {
            final AnalyticsService analytics = AnalyticsService.instance;
            await analytics.logProfileMetrics(
              userId: user!.uid,
              metricType: 'settings',
            );
            if (context.mounted) {
              await RouteUtils.pushNamed(
                context,
                RoutePaths.settingsPage,
              );
            }
          },
          icon: Icon(
            CupertinoIcons.settings,
            color: darkAppColors.lightColor,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(bottom: 20),
        child: BlocListener<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is Unauthenticated) {
              RouteUtils.pushNamedAndRemoveUntil(
                context,
                RoutePaths.onboardPage,
                (route) => route.isFirst,
              );
            }
          },
          child: Padding(
            padding: SizeUtils.horizontalPadding.copyWith(
              top: 12,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      height: 90,
                      width: 90,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            darkAppColors.secondaryBackgroundColor,
                            const Color.fromARGB(255, 64, 64, 64),
                          ],
                        ),
                      ),
                      child: Text(
                        user!.fullName.trim().contains(' ')
                            ? '${user!.fullName.trim().split(" ").first[0].toUpperCase()}'
                                '${user!.fullName.trim().split(" ").last[0].toUpperCase()}'
                            : '${user!.fullName.trim()[0].toUpperCase()}'
                                '${user!.fullName.trim().length > 1 ? user!.fullName.trim()[user!.fullName.trim().length - 1].toUpperCase() : ""}',
                        style: AppTextStyles.heading1.copyWith(
                          fontSize: 32,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${user!.fullName}',
                          style: AppTextStyles.heading2,
                        ),
                        // Container(
                        //   padding: const EdgeInsets.symmetric(
                        //     horizontal: 12,
                        //     vertical: 6,
                        //   ),
                        //   decoration: BoxDecoration(
                        //     color: darkAppColors.deepPurple
                        //         .withAlpha((255 * 0.2).toInt()),
                        //     borderRadius: BorderRadius.circular(20),
                        //   ),
                        //   child: const Row(
                        //     mainAxisSize: MainAxisSize.min,
                        //     children: [
                        //       Icon(
                        //         CupertinoIcons.sparkles,
                        //         size: 20,
                        //         color: Colors.deepPurpleAccent,
                        //       ),
                        //       SizedBox(width: 4),
                        //       Text(
                        //         'Vibe Points: 224',
                        //         style: TextStyle(
                        //           color: Colors.deepPurpleAccent,
                        //           fontSize: 16,
                        //           fontWeight: FontWeight.w600,
                        //         ),
                        //       ),
                        //     ],
                        //   ),
                        // ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(
                  height: 16,
                ),
                _buildStatsSection(),
                const SizedBox(
                  height: 16,
                ),
                _buildActivitySection(),
                const SizedBox(
                  height: 16,
                ),
                _buildForYouSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<dynamic> settingsDialog(BuildContext context) {
    return showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: const Text('Settings'),
        actions: <CupertinoActionSheetAction>[
          // CupertinoActionSheetAction(
          //   onPressed: () {
          //     RouteUtils.pushNamed(
          //       context,
          //       RoutePaths.accountPage,
          //     );
          //     // Handle Account
          //   },
          //   child: Text(
          //     'Account',
          //     style: AppTextStyles.heading4,
          //   ),
          // ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              // Handle FAQ
              FAQDialog.show(context);
            },
            child: Text(
              'FAQ',
              style: AppTextStyles.heading4,
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);

              // Handle Contact Us
              openVibeoWebLink();
            },
            child: Text(
              'Contact Us',
              style: AppTextStyles.heading4,
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              // Handle Privacy Policy
              PrivacyPolicyDialog.show(context);
            },
            child: Text(
              'Privacy Policy',
              style: AppTextStyles.heading4,
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              TCProfileDialog.show(context);
            },
            child: Text(
              'Terms & Conditions',
              style: AppTextStyles.heading4,
            ),
          ),
          CupertinoActionSheetAction(
            isDestructiveAction: true,
            onPressed: () {
              RouteUtils.pop(context);
              showDialog(
                context: context,
                builder: (context) => CupertinoAlertDialog(
                  title: const Text('Delete Account'),
                  content: const Text(
                    'Do you really want to permantly delete your account?',
                  ),
                  actions: [
                    CupertinoDialogAction(
                      isDestructiveAction: true,
                      onPressed: () {
                        RouteUtils.pop(context);
                        context.read<UserBloc>().add(
                              DeleteUser(
                                uid: context.read<UserBloc>().state.user!.uid,
                              ),
                            );
                        context.read<AuthBloc>().add(const SignOut());
                      },
                      child: const Text(
                        'Yes',
                      ),
                    ),
                    CupertinoDialogAction(
                      textStyle: const TextStyle(
                        color: Colors.white54,
                      ),
                      child: const Text(
                        'Cancel',
                      ),
                      onPressed: () {
                        RouteUtils.pop(context);
                      },
                    ),
                  ],
                ),
              );
            },
            child: const Text(
              'Delete Account',
              style: TextStyle(
                fontSize: 18,
              ),
            ),
          ),
          CupertinoActionSheetAction(
            isDestructiveAction: true,
            onPressed: () {
              RouteUtils.pop(context);
              showDialog(
                context: context,
                builder: (context) => CupertinoAlertDialog(
                  title: const Text('Sign out'),
                  content: const Text('Do you really want to sign out?'),
                  actions: [
                    CupertinoDialogAction(
                      isDestructiveAction: true,
                      child: const Text(
                        'Yes',
                      ),
                      onPressed: () {
                        RouteUtils.pop(context);

                        context.read<AuthBloc>().add(const SignOut());
                      },
                    ),
                    CupertinoDialogAction(
                      textStyle: const TextStyle(
                        color: Colors.white54,
                      ),
                      child: const Text(
                        'Cancel',
                      ),
                      onPressed: () {
                        RouteUtils.pop(context);
                      },
                    ),
                  ],
                ),
              );
            },
            child: const Text(
              'Logout',
              style: TextStyle(
                fontSize: 18,
              ),
            ),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text(
            'Cancel',
            style: TextStyle(
              fontSize: 18,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    return FutureBuilder<Map<String, int>>(
      future: _loadVibesInfo(),
      builder: (context, snapshot) {
        final vibesWatched = snapshot.data?['watched'] ?? 0;
        final vibesLiked = snapshot.data?['liked'] ?? 0;

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            boxShadow: AppShadowStyles.baseStyle,
            borderRadius: BorderRadius.circular(16),
            color: darkAppColors.secondaryBackgroundColor,
          ),
          child: Row(
            children: [
              _buildStatItem('Vibes\nWatched', vibesWatched),
              Container(
                height: 40,
                width: 1,
                color: Colors.grey.withAlpha((255 * 0.3).toInt()),
                margin: const EdgeInsets.symmetric(horizontal: 16),
              ),
              _buildStatItem('Vibes\nLiked', vibesLiked),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, int value) {
    return Expanded(
      child: Column(
        children: [
          Text(
            NumberFormat.compact().format(value),
            style: AppTextStyles.heading2.copyWith(
              fontSize: 24,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyBold.copyWith(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Activity',
          style: AppTextStyles.title.copyWith(
            color: Colors.grey,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          clipBehavior: Clip.none,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: yourActivitySection.length,
          itemBuilder: (ctx, index) {
            final isContentCreator =
                context.read<UserBloc>().state.user!.contentCreator;
            if (!isContentCreator && index == 0) return const SizedBox.shrink();

            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: InkWell(
                onTap: () => _handleActivityTap(index),
                borderRadius: const BorderRadius.all(Radius.circular(12)),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: [
                        darkAppColors.secondaryBackgroundColor.withAlpha(
                          (255 * 0.5).toInt(),
                        ),
                        darkAppColors.secondaryBackgroundColor.withAlpha(
                          (255 * 0.3).toInt(),
                        ),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: darkAppColors.deepPurple
                              .withAlpha((255 * 0.3).toInt()),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          _getActivityIcon(index),
                          color: Colors.deepPurpleAccent,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              yourActivitySection[index]['title']!,
                              style: AppTextStyles.heading4.copyWith(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              yourActivitySection[index]['description']!,
                              style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.chevron_right,
                        color: darkAppColors.lightColor
                            .withAlpha((255 * 0.5).toInt()),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  IconData _getActivityIcon(int index) {
    switch (yourActivitySection[index]['title']) {
      case 'Your Vibes':
        return Icons.video_library_rounded;
      case 'Vibes Liked':
        return Icons.favorite_rounded;
      // VIBEPOINTS FEATURE COMMENTED OUT
      // case 'Your Vibe Points Activity':
      //   return CupertinoIcons.sparkles;
      default:
        return Icons.circle;
    }
  }

  Future<void> _handleActivityTap(int index) async {
    final analytics = AnalyticsService.instance;
    await analytics.logProfileMetrics(
      userId: user!.uid,
      metricType: yourActivitySection[index]['title']!,
    );
    if (mounted) {
      await RouteUtils.pushNamed(
        context,
        yourActivitySection[index]['pageID']!,
      );
    }
  }

  Widget _buildForYouSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'For You',
          style: AppTextStyles.title.copyWith(
            color: Colors.grey,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 140,
          child: ListView.builder(
            clipBehavior: Clip.none,
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            itemCount: forYouSection.length,
            itemBuilder: (ctx, index) {
              return Padding(
                padding: EdgeInsets.only(
                  right: 12,
                  left: index == 0 ? 0 : 0,
                ),
                child: InkWell(
                  onTap: () => _handleForYouTap(index),
                  borderRadius: const BorderRadius.all(Radius.circular(12)),
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.7,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: LinearGradient(
                        colors: [
                          darkAppColors.secondaryBackgroundColor
                              .withAlpha((255 * 0.5).toInt()),
                          darkAppColors.secondaryBackgroundColor
                              .withAlpha((255 * 0.3).toInt()),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: darkAppColors.deepPurple
                                .withAlpha((255 * 0.3).toInt()),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            _getForYouIcon(index),
                            color: Colors.deepPurpleAccent,
                            size: 24,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          forYouSection[index]['title']!,
                          style: AppTextStyles.heading4.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getForYouDescription(index),
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  IconData _getForYouIcon(int index) {
    switch (forYouSection[index]['title']) {
      case 'Saved Venues':
        return Icons.place_rounded;
      default:
        return Icons.circle;
    }
  }

  String _getForYouDescription(int index) {
    switch (forYouSection[index]['title']) {
      case 'Saved Venues':
        return 'Check out your favorite venues';
      default:
        return '';
    }
  }

  Future<void> _handleForYouTap(int index) async {
    final analytics = AnalyticsService.instance;
    await analytics.logProfileMetrics(
      userId: user!.uid,
      metricType: forYouSection[index]['title']!,
    );
    if (mounted) {
      await RouteUtils.pushNamed(
        context,
        forYouSection[index]['pageID']!,
      );
    }
  }
}
