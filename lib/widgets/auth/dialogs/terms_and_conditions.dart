import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:vibeo/components/buildScrollableWithFade.dart';
import 'package:vibeo/components/loading_widget.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/auth/auth_button.dart';

class TermsAndConditionsDialog extends StatefulWidget {
  const TermsAndConditionsDialog({super.key});

  static Future<void> show(BuildContext context) async {
    return showDialog(
      context: context,
      barrierColor: Colors.black.withAlpha(178),
      barrierDismissible: false,
      builder: (context) => const PopScope(
        canPop: false,
        child: TermsAndConditionsDialog(),
      ),
    );
  }

  @override
  State<TermsAndConditionsDialog> createState() =>
      _TermsAndConditionsDialogState();
}

class _TermsAndConditionsDialogState extends State<TermsAndConditionsDialog> {
  final ScrollController _scrollController = ScrollController();
  bool _isScrolledToBottom = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    if (_scrollController.offset >=
            _scrollController.position.maxScrollExtent &&
        !_scrollController.position.outOfRange) {
      setState(() {
        _isScrolledToBottom = true;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 32),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(24)),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Container(
                decoration: BoxDecoration(
                  color: darkAppColors.secondaryBackgroundColor.withAlpha(76),
                  borderRadius: const BorderRadius.all(Radius.circular(24)),
                  border: Border.all(
                    color: Colors.white.withAlpha(51),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(51),
                      blurRadius: 15,
                      spreadRadius: -5,
                      offset: const Offset(0, 10),
                    ),
                    BoxShadow(
                      color: Colors.white.withAlpha(26),
                      blurRadius: 3,
                      spreadRadius: -1,
                      offset: const Offset(0, -1),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Align(
                      alignment: Alignment.topRight,
                      child: Padding(
                        padding: SizeUtils.horizontalPadding.copyWith(
                          top: 20,
                        ),
                        child: IconButton(
                          onPressed: () {
                            RouteUtils.pop(context);
                          },
                          icon: const Icon(
                            CupertinoIcons.clear,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: RawScrollbar(
                        thumbColor: Colors.white.withAlpha(76),
                        radius: const Radius.circular(20),
                        thickness: 5,
                        controller: _scrollController,
                        child: buildScrollableWithFade(
                          child: SingleChildScrollView(
                            controller: _scrollController,
                            child: Padding(
                              padding: SizeUtils.horizontalPadding.copyWith(
                                top: 20,
                                bottom: 20,
                              ),
                              child: FutureBuilder<String>(
                                future: DefaultAssetBundle.of(context)
                                    .loadString('assets/files/terms.md'),
                                builder: (context, snapshot) {
                                  if (snapshot.hasData) {
                                    return MarkdownBody(
                                      data: snapshot.data!,
                                      styleSheet: MarkdownStyleSheet(
                                        p: TextStyle(
                                          color: Colors.white.withAlpha(204),
                                          fontSize: 16,
                                          height: 1.5,
                                        ),
                                        h1: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 22,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        h2: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    );
                                  }
                                  return const LoadingWidget();
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: authButton(
                        title: 'I Agree',
                        onTap: () => RouteUtils.pop(context),
                        enabled: _isScrolledToBottom,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
