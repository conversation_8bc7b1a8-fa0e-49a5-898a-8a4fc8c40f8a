import 'package:flutter/material.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

class ProcessingAnimation extends StatefulWidget {
  const ProcessingAnimation({super.key});

  @override
  State<ProcessingAnimation> createState() => _ProcessingAnimationState();
}

class _ProcessingAnimationState extends State<ProcessingAnimation>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _textController;

  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _textFadeAnimation;

  int _currentTextIndex = 0;
  final List<String> _processingTexts = [
    'Analyzing your preferences...',
    'Finding perfect matches...',
    'Curating your experience...',
    'Almost ready!',
  ];

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_rotationController);

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _textFadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    _startTextCycle();
  }

  Future<void> _startTextCycle() async {
    for (int i = 0; i < _processingTexts.length; i++) {
      if (mounted) {
        setState(() {
          _currentTextIndex = i;
        });

        await _textController.forward();
        await Future.delayed(const Duration(milliseconds: 800));
        await _textController.reverse();

        if (i < _processingTexts.length - 1) {
          await Future.delayed(const Duration(milliseconds: 200));
        }
      }
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated loading indicator
          AnimatedBuilder(
            animation: Listenable.merge([
              _rotationController,
              _pulseController,
            ]),
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Transform.rotate(
                  angle: _rotationAnimation.value * 2 * 3.14159,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          darkAppColors.primary,
                          darkAppColors.deepPurple,
                          darkAppColors.secondary,
                          darkAppColors.primary,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: darkAppColors.deepPurple.withAlpha(102),
                          blurRadius: 30,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: darkAppColors.backgroundColor,
                      ),
                      child: const Icon(
                        Icons.auto_awesome,
                        color: Colors.white,
                        size: 48,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 48),

          // Processing text
          Container(
            height: 60,
            alignment: Alignment.center,
            child: AnimatedBuilder(
              animation: _textController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _textFadeAnimation,
                  child: Text(
                    _processingTexts[_currentTextIndex],
                    style: AppTextStyles.title.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // Subtitle
          Text(
            'Creating your personalized recommendations',
            style: AppTextStyles.body.copyWith(
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          // Progress dots
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(3, (index) {
              return AnimatedBuilder(
                animation: _rotationController,
                builder: (context, child) {
                  final delay = index * 0.3;
                  final animationValue =
                      (_rotationController.value + delay) % 1.0;
                  final opacity = (animationValue < 0.5)
                      ? animationValue * 2
                      : (1.0 - animationValue) * 2;

                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: darkAppColors.primary.withOpacity(opacity),
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
